```mermaid
erDiagram
    %% 核心权限管理实体
    sys_user {
        int id PK
        string username UK "用户名"
        string password "密码"
        string psalt "密码盐值"
        string nickname "昵称"
        string avatar "头像"
        string qq "QQ号"
        string email "邮箱"
        string phone "手机号"
        string remark "备注"
        tinyint status "状态"
        int dept_id FK "部门ID"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    sys_role {
        int id PK
        string name UK "角色名称"
        string value UK "角色标识"
        string remark "角色描述"
        tinyint status "状态"
        tinyint default "是否默认角色"
        int create_by "创建者"
        int update_by "更新者"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    sys_menu {
        int id PK
        int parent_id "父级菜单ID"
        string name "菜单名称"
        string path "前端路由路径"
        string permission "权限标识"
        tinyint type "菜单类型:0菜单,1目录,2权限"
        string icon "菜单图标"
        int order_no "排序号"
        string component "前端组件路径"
        tinyint is_ext "是否外链"
        tinyint ext_open_mode "外链打开方式"
        tinyint keep_alive "是否缓存页面"
        tinyint show "是否显示"
        string active_menu "高亮菜单"
        tinyint status "状态"
        int create_by "创建者"
        int update_by "更新者"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    sys_dept {
        int id PK
        string name "部门名称"
        int orderNo "排序"
        string mpath "物化路径"
        int parentId FK "父部门ID"
        int create_by "创建者"
        int update_by "更新者"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    %% 中间表
    sys_user_roles {
        int user_id PK,FK
        int role_id PK,FK
    }
    
    sys_role_menus {
        int role_id PK,FK
        int menu_id PK,FK
    }
    
    %% 认证令牌实体
    user_access_tokens {
        string id PK "UUID"
        string value "JWT Token值"
        datetime expired_at "令牌过期时间"
        datetime created_at "令牌创建时间"
        int user_id FK "用户ID"
    }
    
    user_refresh_tokens {
        string id PK "UUID"
        string value "刷新令牌值"
        datetime expired_at "令牌过期时间"
        datetime created_at "令牌创建时间"
        string accessTokenId FK "访问令牌ID"
    }
    
    %% 系统配置实体
    sys_dict_type {
        int id PK
        string name "字典名称"
        string code UK "字典编码"
        tinyint status "状态"
        string remark "备注"
        int create_by "创建者"
        int update_by "更新者"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    sys_dict_item {
        int id PK
        string label "字典项键名"
        string value "字典项值"
        int orderNo "字典项排序"
        tinyint status "状态"
        string remark "备注"
        int type_id FK "字典类型ID"
        int create_by "创建者"
        int update_by "更新者"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    sys_config {
        int id PK
        string name "配置名"
        string key UK "配置键名"
        string value "配置值"
        string remark "配置描述"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    %% 任务调度实体
    sys_task {
        int id PK
        string name UK "任务名"
        string service "任务标识"
        tinyint type "任务类型:0cron,1间隔"
        tinyint status "任务状态:0禁用,1启用"
        datetime start_time "开始时间"
        datetime end_time "结束时间"
        int limit "间隔时间"
        string cron "cron表达式"
        int every "执行次数"
        text data "任务参数"
        text job_opts "任务配置"
        string remark "任务描述"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    sys_task_log {
        int id PK
        tinyint status "任务状态:0失败,1成功"
        text detail "任务日志信息"
        int consume_time "任务耗时"
        int task_id FK "任务ID"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    %% 日志实体
    sys_login_log {
        int id PK
        string ip "IP地址"
        string address "地址"
        string provider "登录方式"
        string ua "浏览器UA"
        int user_id FK "用户ID"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    sys_captcha_log {
        int id PK
        int user_id "用户ID"
        string account "账号"
        string code "验证码"
        string provider "验证码提供方:sms,email"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    %% 文件存储实体
    tool_storage {
        int id PK
        string name "文件名"
        string fileName "真实文件名"
        string ext_name "扩展名"
        string path "文件路径"
        string type "文件类型"
        string size "文件大小"
        int user_id "上传用户ID"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    %% 业务实体
    todo {
        int id PK
        string value "todo内容"
        tinyint status "完成状态"
        int user_id FK "用户ID"
        datetime created_at "创建时间"
        datetime updated_at "更新时间"
    }
    
    %% 关系定义
    %% 用户与角色：多对多
    sys_user ||--o{ sys_user_roles : "用户角色关联"
    sys_role ||--o{ sys_user_roles : "角色用户关联"
    
    %% 角色与菜单：多对多
    sys_role ||--o{ sys_role_menus : "角色菜单关联"
    sys_menu ||--o{ sys_role_menus : "菜单角色关联"
    
    %% 用户与部门：多对一
    sys_dept ||--o{ sys_user : "部门用户"
    
    %% 部门树形结构：自关联
    sys_dept ||--o{ sys_dept : "父子部门"
    
    %% 用户与访问令牌：一对多
    sys_user ||--o{ user_access_tokens : "用户令牌"
    
    %% 访问令牌与刷新令牌：一对一
    user_access_tokens ||--|| user_refresh_tokens : "令牌对"
    
    %% 字典类型与字典项：一对多
    sys_dict_type ||--o{ sys_dict_item : "字典项"
    
    %% 任务与任务日志：一对多
    sys_task ||--o{ sys_task_log : "任务日志"
    
    %% 用户与登录日志：一对多
    sys_user ||--o{ sys_login_log : "登录日志"
    
    %% 用户与TODO：一对多
    sys_user ||--o{ todo : "用户TODO"
    
    %% 菜单树形结构：自关联
    sys_menu ||--o{ sys_menu : "父子菜单"
```