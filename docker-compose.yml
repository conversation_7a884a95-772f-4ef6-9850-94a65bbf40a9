version: '3'

services:
  nest-admin-web:
    image: buqiyuan/vue3-antdv-admin:stable
    container_name: nest-admin-web
    volumes:
      - ./deploy/web:/etc/nginx/conf.d
    ports:
      - '80:80'
    restart: always
    networks:
      - nest_admin_net
  mysql:
    image: mysql:latest
    container_name: nest-admin-mysql
    restart: always
    env_file:
      - .env
      - .env.production
    environment:
      - MYSQL_HOST=${DB_HOST}
      - MYSQL_PORT=${DB_PORT}
      - MYSQL_DATABASE=${DB_DATABASE}
      - MYSQL_USERNAME=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
    ports:
      - '${DB_PORT}:3306'
    command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_general_ci # 设置utf8字符集
    volumes:
      - ./__data/mysql/:/var/lib/mysql/ # ./__data/mysql/ 路径可以替换成自己的路径
      - ./deploy/sql/:/docker-entrypoint-initdb.d/ # 初始化的脚本，若 ./__data/mysql/ 文件夹存在数据，则不会执行初始化脚本
    networks:
      - nest_admin_net

  redis:
    image: redis:alpine
    container_name: nest-admin-redis
    restart: always
    env_file:
      - .env
      - .env.production
    ports:
      - '${REDIS_PORT}:6379'
    command: >
      --requirepass ${REDIS_PASSWORD}
    networks:
      - nest_admin_net

  nest-admin-server:
    # build: 从当前路径构建镜像
    build:
      context: .
      args:
        - PROJECT_DIR=${PROJECT_DIR}
    image: buqiyuan/nest-admin-server:stable
    container_name: nest-admin-server
    restart: always
    env_file:
      - .env
      - .env.production
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - '${APP_PORT}:${APP_PORT}'
    volumes:
      - ./logs/:${PROJECT_DIR}/logs/ # ./logs 日志文件挂载到容器外部
    # 当前服务启动之前先要把depends_on指定的服务启动起来才行
    depends_on:
      - mysql
      - redis
    networks:
      - nest_admin_net

networks:
  nest_admin_net:
    name: nest_admin_net
