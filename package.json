{"name": "nest-admin", "version": "2.0.0", "private": true, "packageManager": "pnpm@9.1.0", "license": "MIT", "engines": {"node": ">=20.0.0", "pnpm": ">=9"}, "scripts": {"postinstall": "npm run gen-env-types", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "dev": "npm run start", "dev:debug": "npm run start:debug", "repl": "npm run start -- --entryFile repl", "bundle": "rimraf out && npm run build && ncc build dist/main.js -o out -m -t && chmod +x out/index.js", "start": "cross-env NODE_ENV=development nest start -w --path tsconfig.json", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "prod": "cross-env NODE_ENV=production pm2-runtime start ecosystem.config.js", "prod:pm2": "cross-env NODE_ENV=production pm2 restart ecosystem.config.js", "prod:stop": "pm2 stop ecosystem.config.js", "prod:debug": "cross-env NODE_ENV=production nest start --debug --watch", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "jest", "test:watch": "jest --watch", "doc": "compodoc -p tsconfig.json -s", "gen-env-types": "npx tsx scripts/genEnvTypes.ts || true", "typeorm": "cross-env NODE_ENV=development typeorm-ts-node-esm -d ./dist/config/database.config.js", "migration:create": "npm run typeorm migration:create ./src/migrations/initData", "migration:generate": "npm run typeorm migration:generate ./src/migrations/update-table_$(echo $npm_package_version | sed 's/\\./_/g')", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert", "cleanlog": "rimraf logs", "docker:build:dev": "docker compose --env-file .env --env-file .env.development up --build", "docker:build": "docker compose --env-file .env --env-file .env.production up --build", "docker:prod:up": "docker compose -f docker-compose.prod.yml --env-file .env --env-file .env.production up -d --pull=always", "docker:up": "docker compose --env-file .env --env-file .env.production up -d --no-build", "docker:down": "docker compose --env-file .env --env-file .env.production down", "docker:rmi": "docker compose --env-file .env --env-file .env.production stop nest-admin-server && docker container rm nest-admin-server && docker rmi nest-admin-server", "docker:logs": "docker compose --env-file .env --env-file .env.production logs -f"}, "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.1.1", "@liaoliaots/nestjs-redis": "^10.0.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.13", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.13", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.0.13", "@nestjs/platform-socket.io": "^11.0.13", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.1.1", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.13", "@socket.io/redis-adapter": "^8.3.0", "@socket.io/redis-emitter": "^5.1.0", "axios": "^1.8.4", "bull": "^4.16.5", "cache-manager": "^6.4.2", "cache-manager-ioredis-yet": "^2.1.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cron": "^4.1.4", "cron-parser": "^5.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "16.4.7", "handlebars": "^4.7.8", "helmet": "^8.1.0", "ioredis": "^5.6.0", "lodash": "^4.17.21", "mysql2": "^3.14.0", "nanoid": "^3.3.7", "nestjs-cls": "^5.4.2", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "qiniu": "^7.14.0", "qs": "^6.14.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "stacktrace-js": "^2.0.2", "svg-captcha": "^1.4.0", "systeminformation": "^5.25.11", "ts-node": "^10.9.2", "typeorm": "0.3.22", "ua-parser-js": "^2.0.3", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@antfu/eslint-config": "^4.11.0", "@compodoc/compodoc": "^1.1.26", "@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.3", "@nestjs/testing": "^11.0.13", "@types/cache-manager": "^4.0.6", "@types/jest": "29.5.14", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/node": "^22.14.0", "@types/passport-jwt": "^4.0.1", "@types/qs": "^6.9.18", "@types/supertest": "^6.0.3", "@types/ua-parser-js": "^0.7.39", "cross-env": "^7.0.3", "eslint": "^9.24.0", "jest": "^29.7.0", "lint-staged": "^15.5.0", "rimraf": "^6.0.1", "simple-git-hooks": "^2.12.1", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.1", "ts-loader": "^9.5.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.3", "typescript": "~5.8.3"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {}}}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "moduleNameMapper": {"^~/(.*)$": "<rootDir>/$1"}, "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}