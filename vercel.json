{
  "$schema": "https://openapi.vercel.sh/vercel.json",
  "installCommand": "pnpm install",
  "buildCommand": "pnpm build",
  // 由于项目中使用了路径别名，暂时无法简单的部署到 vercel: https://github.com/vercel/vercel/issues/2832
  "builds": [
    {
      "src": "src/main.ts",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "src/main.ts",
      "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"]
    }
  ],
  "env": {
    "NODE_ENV": "development"
  }
}
