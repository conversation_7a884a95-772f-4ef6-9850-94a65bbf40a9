{"compilerOptions": {"incremental": true, "target": "ES2022", "lib": ["ESNext"], "emitDecoratorMetadata": true, "experimentalDecorators": true, "baseUrl": "./", "module": "commonjs", "paths": {"~/*": ["src/*"]}, "strictBindCallApply": false, "strictNullChecks": false, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "declaration": true, "outDir": "./dist", "removeComments": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "skipLibCheck": true}, "exclude": ["node_modules", "scripts", "dist"]}